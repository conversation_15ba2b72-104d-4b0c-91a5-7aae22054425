#!/usr/bin/env python3
"""
Layout布局器视频生成测试

测试Layout布局器在实际视频生成中的应用，包括：
- 自动布局冲突检测
- 布局引擎自动解决方案
- 与现有视频生成流程的集成
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from enhanced_generator import EnhancedJingwuGenerator
from lyric_timeline import (
    LyricTimeline,
    LyricDisplayMode,
    LyricStyle,
    create_enhanced_timeline,
    create_simple_timeline
)

def create_test_audio():
    """创建测试音频文件（如果不存在）"""
    test_audio_path = Path("test_audio.wav")
    
    if not test_audio_path.exists():
        print("创建测试音频文件...")
        try:
            # 创建一个简单的测试音频（10秒静音）
            from moviepy.editor import AudioClip
            import numpy as np
            
            def make_frame(t):
                return np.array([0.0, 0.0])  # 立体声静音
            
            audio_clip = AudioClip(make_frame, duration=10, fps=22050)
            audio_clip.write_audiofile(str(test_audio_path), verbose=False, logger=None)
            print(f"✅ 测试音频已创建: {test_audio_path}")
            
        except Exception as e:
            print(f"❌ 创建测试音频失败: {e}")
            return None
    
    return str(test_audio_path)

def test_layout_engine_video_generation():
    """测试使用布局引擎的视频生成"""
    print("🎬 测试Layout布局器视频生成")
    print("=" * 50)
    
    # 创建测试音频
    audio_path = create_test_audio()
    if not audio_path:
        print("❌ 无法创建测试音频，跳过视频生成测试")
        return False
    
    # 创建生成器
    generator = EnhancedJingwuGenerator(width=720, height=1280, fps=24)
    
    # 创建测试歌词数据 - 故意创建重叠的布局
    main_lyrics = [
        (0.0, "这是主歌词第一句"),
        (2.0, "这是主歌词第二句"),
        (4.0, "这是主歌词第三句"),
        (6.0, "这是主歌词第四句"),
        (8.0, "这是主歌词第五句")
    ]
    
    aux_lyrics = [
        (0.0, "This is English subtitle line 1"),
        (2.0, "This is English subtitle line 2"),
        (4.0, "This is English subtitle line 3"),
        (6.0, "This is English subtitle line 4"),
        (8.0, "This is English subtitle line 5")
    ]
    
    # 创建时间轴 - 故意设置可能重叠的位置
    main_timeline = LyricTimeline(
        lyrics_data=main_lyrics,
        language="chinese",
        style=LyricStyle(font_size=80, highlight_color='#FFD700'),
        display_mode=LyricDisplayMode.ENHANCED_PREVIEW,
        element_id="main_chinese",
        priority=1,
        is_flexible=False  # 主歌词位置固定
    )
    
    # 副歌词设置在可能重叠的位置
    aux_timeline = LyricTimeline(
        lyrics_data=aux_lyrics,
        language="english",
        style=LyricStyle(font_size=60, font_color='white'),
        display_mode=LyricDisplayMode.SIMPLE_FADE,
        element_id="aux_english",
        priority=10,
        is_flexible=True  # 副歌词位置可调整
    )
    
    # 设置副歌词在可能重叠的位置（故意制造冲突）
    aux_timeline.set_display_mode(
        LyricDisplayMode.SIMPLE_FADE,
        y_position=640,  # 设置在中心位置，可能与主歌词重叠
        is_highlighted=False
    )
    
    print(f"✅ 主时间轴: {main_timeline.element_id}")
    print(f"✅ 副时间轴: {aux_timeline.element_id}")
    
    # 输出路径
    output_path = "test_layout_output.mp4"
    
    print("\n🔧 开始视频生成（使用布局引擎）...")
    
    # 生成视频 - 启用布局引擎
    success = generator._generate_video_with_timelines(
        main_timeline=main_timeline,
        aux_timeline=aux_timeline,
        audio_path=audio_path,
        output_path=output_path,
        background_image=None,
        t_max_sec=10.0,
        use_layout_engine=True  # 启用布局引擎
    )
    
    if success:
        print(f"\n✅ 视频生成成功: {output_path}")
        
        # 检查文件是否存在
        output_file = Path(output_path)
        if output_file.exists():
            file_size = output_file.stat().st_size / (1024 * 1024)  # MB
            print(f"📁 文件大小: {file_size:.1f} MB")
        else:
            print("❌ 输出文件不存在")
            return False
    else:
        print("❌ 视频生成失败")
        return False
    
    return True

def test_traditional_layout_comparison():
    """测试传统布局方式（对比测试）"""
    print("\n🔄 测试传统布局方式（对比）")
    print("=" * 50)
    
    # 创建测试音频
    audio_path = create_test_audio()
    if not audio_path:
        print("❌ 无法创建测试音频，跳过对比测试")
        return False
    
    # 创建生成器
    generator = EnhancedJingwuGenerator(width=720, height=1280, fps=24)
    
    # 使用相同的歌词数据
    main_lyrics = [
        (0.0, "传统布局主歌词第一句"),
        (2.0, "传统布局主歌词第二句"),
        (4.0, "传统布局主歌词第三句")
    ]
    
    aux_lyrics = [
        (0.0, "Traditional layout English subtitle 1"),
        (2.0, "Traditional layout English subtitle 2"),
        (4.0, "Traditional layout English subtitle 3")
    ]
    
    main_timeline = create_enhanced_timeline(main_lyrics, "chinese", "traditional_main", priority=1)
    aux_timeline = create_simple_timeline(aux_lyrics, "english", "traditional_aux", priority=10)
    
    # 手动设置副歌词位置（传统方式）
    aux_timeline.set_display_mode(
        LyricDisplayMode.SIMPLE_FADE,
        y_position=640,  # 可能重叠的位置
        is_highlighted=False
    )
    
    output_path = "test_traditional_output.mp4"
    
    print("🔧 开始视频生成（传统布局）...")
    
    # 生成视频 - 禁用布局引擎
    success = generator._generate_video_with_timelines(
        main_timeline=main_timeline,
        aux_timeline=aux_timeline,
        audio_path=audio_path,
        output_path=output_path,
        background_image=None,
        t_max_sec=6.0,
        use_layout_engine=False  # 禁用布局引擎
    )
    
    if success:
        print(f"✅ 传统布局视频生成成功: {output_path}")
        return True
    else:
        print("❌ 传统布局视频生成失败")
        return False

def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        "test_audio.wav",
        "test_layout_output.mp4",
        "test_traditional_output.mp4"
    ]
    
    print("\n🧹 清理测试文件...")
    for file_path in test_files:
        file_obj = Path(file_path)
        if file_obj.exists():
            try:
                file_obj.unlink()
                print(f"  ✅ 已删除: {file_path}")
            except Exception as e:
                print(f"  ❌ 删除失败 {file_path}: {e}")

def main():
    """运行视频生成测试"""
    print("🎬 Layout布局器视频生成测试")
    print("=" * 60)
    
    try:
        # 测试布局引擎视频生成
        layout_success = test_layout_engine_video_generation()
        
        # 测试传统布局对比
        traditional_success = test_traditional_layout_comparison()
        
        if layout_success and traditional_success:
            print("\n🎉 所有视频生成测试通过！")
            print("💡 Layout布局器已成功集成到视频生成流程中")
            
            # 询问是否清理测试文件
            try:
                response = input("\n是否清理测试文件？(y/N): ").strip().lower()
                if response in ['y', 'yes']:
                    cleanup_test_files()
                else:
                    print("保留测试文件以供查看")
            except KeyboardInterrupt:
                print("\n保留测试文件")
            
            return True
        else:
            print("\n❌ 部分测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
